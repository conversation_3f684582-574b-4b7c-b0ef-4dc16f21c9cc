package integration

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/auth"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/config"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockEdgeAPIClient 模拟EdgeAPI客户端
type MockEdgeAPIClient struct{}

func (m *MockEdgeAPIClient) Close() error {
	return nil
}

// setupTestConfig 设置测试配置
func setupTestConfig() *config.Config {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host: "localhost",
			Port: 8080,
			Mode: "debug",
		},
		JWT: config.JWTConfig{
			SecretKey:        "test_jwt_secret_key_for_integration_testing",
			TokenDuration:    24 * time.Hour,
			Issuer:           "EdgeOpenAPI_Test",
			RefreshThreshold: 30 * time.Minute,
		},
	}

	// 注意：这是一个简化的设置，实际应用中应该通过LoadConfig函数
	return cfg
}

// setupTestRouter 设置测试路由
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(gin.Recovery())

	// 设置测试配置
	testConfig := setupTestConfig()

	// 创建JWT管理器
	jwtManager := auth.NewJWTManager(
		testConfig.JWT.SecretKey,
		testConfig.JWT.TokenDuration,
		testConfig.JWT.Issuer,
	)

	// 设置路由
	api := router.Group("/api")
	v1 := api.Group("/v1")

	// 认证相关路由（简化版本，不依赖复杂的处理器）
	auth := v1.Group("/auth")
	{
		auth.POST("/validate", func(c *gin.Context) {
			// 从Authorization头中提取JWT令牌
			authHeader := c.GetHeader("Authorization")
			if authHeader == "" {
				c.JSON(http.StatusUnauthorized, gin.H{
					"code":    http.StatusUnauthorized,
					"message": "Missing authorization header",
				})
				return
			}

			tokenParts := strings.SplitN(authHeader, " ", 2)
			if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
				c.JSON(http.StatusUnauthorized, gin.H{
					"code":    http.StatusUnauthorized,
					"message": "Invalid authorization header format",
				})
				return
			}

			token := tokenParts[1]
			claims, err := jwtManager.ValidateToken(token)
			if err != nil {
				c.JSON(http.StatusUnauthorized, gin.H{
					"code":    http.StatusUnauthorized,
					"message": "Invalid or expired token",
				})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"message": "success",
				"data": gin.H{
					"valid":      true,
					"message":    "Token is valid",
					"user_id":    claims.UserID,
					"username":   claims.Username,
					"expires_at": claims.RegisteredClaims.ExpiresAt.Unix(),
				},
			})
		})
	}

	// 需要认证的路由
	protected := v1.Group("/protected")
	protected.Use(func(c *gin.Context) {
		// 简化的JWT中间件
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Missing authorization header",
			})
			c.Abort()
			return
		}

		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]
		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// 将用户信息设置到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Next()
	})
	{
		protected.GET("/test", func(c *gin.Context) {
			userID, _ := c.Get("user_id")
			username, _ := c.Get("username")

			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"message": "success",
				"data": gin.H{
					"user_id":  userID,
					"username": username,
					"message":  "Access granted to protected resource",
				},
			})
		})
	}

	return router
}

func TestJWTAuthenticationFlow(t *testing.T) {
	router := setupTestRouter()
	
	// 创建JWT管理器用于测试
	jwtManager := auth.NewJWTManager(
		"test_jwt_secret_key_for_integration_testing",
		24*time.Hour,
		"EdgeOpenAPI_Test",
	)
	
	t.Run("JWT Token Generation and Validation", func(t *testing.T) {
		// 生成测试JWT令牌
		userID := int64(12345)
		username := "testuser"
		email := "<EMAIL>"
		role := "user"
		
		token, err := jwtManager.GenerateToken(userID, username, email, "", role)
		require.NoError(t, err)
		require.NotEmpty(t, token)
		
		// 测试受保护的端点
		req := httptest.NewRequest("GET", "/api/v1/protected/test", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		
		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		
		assert.Equal(t, float64(200), response["code"])
		assert.Equal(t, "success", response["message"])
		
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(userID), data["user_id"])
		assert.Equal(t, username, data["username"])
	})
	
	t.Run("JWT Token Validation Endpoint", func(t *testing.T) {
		// 生成测试JWT令牌
		userID := int64(67890)
		username := "validateuser"
		role := "user"
		
		token, err := jwtManager.GenerateToken(userID, username, "", "", role)
		require.NoError(t, err)
		
		// 测试令牌验证端点
		req := httptest.NewRequest("POST", "/api/v1/auth/validate", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusOK, w.Code)
		
		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		
		assert.Equal(t, float64(200), response["code"])
		
		data := response["data"].(map[string]interface{})
		assert.Equal(t, true, data["valid"])
		assert.Equal(t, float64(userID), data["user_id"])
		assert.Equal(t, username, data["username"])
	})
	
	t.Run("Unauthorized Access Without Token", func(t *testing.T) {
		// 测试没有令牌的访问
		req := httptest.NewRequest("GET", "/api/v1/protected/test", nil)
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusUnauthorized, w.Code)
		
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		
		assert.Equal(t, float64(http.StatusUnauthorized), response["code"])
		assert.Contains(t, response["message"], "Missing authorization header")
	})
	
	t.Run("Unauthorized Access With Invalid Token", func(t *testing.T) {
		// 测试无效令牌的访问
		req := httptest.NewRequest("GET", "/api/v1/protected/test", nil)
		req.Header.Set("Authorization", "Bearer invalid.token.here")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusUnauthorized, w.Code)
		
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		
		assert.Equal(t, float64(http.StatusUnauthorized), response["code"])
		assert.Contains(t, response["message"], "Invalid or expired token")
	})
	
	t.Run("Invalid Authorization Header Format", func(t *testing.T) {
		// 测试错误的Authorization头格式
		req := httptest.NewRequest("GET", "/api/v1/protected/test", nil)
		req.Header.Set("Authorization", "InvalidFormat token")
		
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		
		assert.Equal(t, http.StatusUnauthorized, w.Code)
		
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)
		
		assert.Equal(t, float64(http.StatusUnauthorized), response["code"])
		assert.Contains(t, response["message"], "Invalid authorization header format")
	})
}

func TestJWTMiddlewareSkipPaths(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.Use(gin.Recovery())

	// 添加健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	router.GET("/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "pong"})
	})

	// 设置测试配置
	testConfig := setupTestConfig()

	// 创建JWT管理器
	jwtManager := auth.NewJWTManager(
		testConfig.JWT.SecretKey,
		testConfig.JWT.TokenDuration,
		testConfig.JWT.Issuer,
	)

	// 添加简化的JWT中间件到所有路由
	router.Use(func(c *gin.Context) {
		// 跳过不需要认证的端点
		if c.Request.URL.Path == "/health" ||
			c.Request.URL.Path == "/ping" ||
			c.Request.URL.Path == "/api/v1/auth/login" {
			c.Next()
			return
		}

		// 从Authorization头中提取JWT令牌
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Missing authorization header",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// 验证JWT令牌
		_, err := jwtManager.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Invalid or expired token",
			})
			c.Abort()
			return
		}

		c.Next()
	})

	// 添加登录端点
	api := router.Group("/api")
	v1 := api.Group("/v1")
	auth := v1.Group("/auth")
	{
		auth.POST("/login", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{"message": "login endpoint"})
		})
	}
	
	skipPaths := []string{
		"/health",
		"/ping",
		"/api/v1/auth/login",
	}
	
	for _, path := range skipPaths {
		t.Run("Skip path: "+path, func(t *testing.T) {
			var req *http.Request
			if path == "/api/v1/auth/login" {
				// POST请求需要请求体
				loginData := map[string]string{
					"username": "testuser",
					"password": "testpass",
				}
				jsonData, _ := json.Marshal(loginData)
				req = httptest.NewRequest("POST", path, bytes.NewBuffer(jsonData))
				req.Header.Set("Content-Type", "application/json")
			} else {
				req = httptest.NewRequest("GET", path, nil)
			}
			
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)
			
			// 这些路径应该不需要认证，不应该返回401
			assert.NotEqual(t, http.StatusUnauthorized, w.Code, "Path %s should skip JWT middleware", path)
		})
	}
}
