# EdgeOpenAPI JWT认证指南

## 概述

EdgeOpenAPI实现了基于JWT（JSON Web Token）的认证系统，作为EdgeAPI现有认证体系的补充。该系统专为HTTP API访问设计，提供了用户友好的认证方式，同时保持与EdgeAPI认证体系的完全兼容性。

## 认证架构

### 双重认证机制

EdgeOpenAPI支持两种认证方式：

1. **JWT令牌认证**（推荐用于用户登录）
   - 用于username/password登录后的API访问
   - 支持令牌刷新和过期管理
   - 适合前端应用和移动应用

2. **API Key认证**（用于服务间调用）
   - 复用EdgeAPI的AccessKey/AccessToken机制
   - 适合服务器到服务器的调用
   - 与EdgeAPI完全兼容

### 认证流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant EdgeOpenAPI as EdgeOpenAPI
    participant EdgeAPI as EdgeAPI

    Note over Client,EdgeAPI: JWT认证流程
    Client->>EdgeOpenAPI: POST /api/v1/auth/login
    EdgeOpenAPI->>EdgeAPI: gRPC LoginUser
    EdgeAPI-->>EdgeOpenAPI: 用户验证结果
    EdgeOpenAPI->>EdgeOpenAPI: 生成JWT令牌
    EdgeOpenAPI-->>Client: 返回JWT令牌

    Note over Client,EdgeAPI: API调用流程
    Client->>EdgeOpenAPI: API请求 + JWT令牌
    EdgeOpenAPI->>EdgeOpenAPI: 验证JWT令牌
    EdgeOpenAPI->>EdgeAPI: gRPC API调用
    EdgeAPI-->>EdgeOpenAPI: API响应
    EdgeOpenAPI-->>Client: HTTP响应
```

## JWT令牌结构

### JWT Claims

```json
{
  "user_id": 12345,
  "username": "enterprise_user",
  "email": "<EMAIL>",
  "mobile": "13800138000",
  "role": "user",
  "iss": "EdgeOpenAPI",
  "sub": "enterprise_user",
  "aud": ["EdgeOpenAPI"],
  "exp": 1640995200,
  "nbf": 1640908800,
  "iat": 1640908800
}
```

### 字段说明

- `user_id`: EdgeAPI中的用户ID
- `username`: 用户名（支持用户名、邮箱、手机号）
- `email`: 用户邮箱（如果用邮箱登录）
- `mobile`: 用户手机号（如果用手机号登录）
- `role`: 用户角色（固定为"user"）
- `iss`: 令牌发行者
- `sub`: 令牌主题（用户名）
- `aud`: 令牌受众
- `exp`: 过期时间
- `nbf`: 生效时间
- `iat`: 签发时间

## API端点

### 1. 用户登录

**端点**: `POST /api/v1/auth/login`

**请求体**:
```json
{
  "username": "<EMAIL>",
  "password": "secure_password"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "message": "Login successful",
    "user_id": 12345,
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer"
  },
  "timestamp": 1640995200,
  "request_id": "req_12345678"
}
```

### 2. 令牌验证

**端点**: `POST /api/v1/auth/validate`

**请求头**:
```
Authorization: Bearer <JWT_TOKEN>
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "valid": true,
    "message": "Token is valid",
    "user_id": 12345,
    "username": "<EMAIL>",
    "expires_at": 1640995200
  }
}
```

### 3. 令牌刷新

**端点**: `POST /api/v1/auth/refresh`

**请求头**:
```
Authorization: Bearer <CURRENT_JWT_TOKEN>
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "message": "Token refreshed successfully"
  }
}
```

## 配置说明

### JWT配置项

```yaml
jwt:
  secret_key: "your_jwt_secret_key_here_change_in_production"
  token_duration: "24h"
  issuer: "EdgeOpenAPI"
  refresh_threshold: "30m"
```

- `secret_key`: JWT签名密钥（生产环境必须更改）
- `token_duration`: JWT令牌有效期
- `issuer`: JWT发行者标识
- `refresh_threshold`: 令牌刷新阈值（过期前多久可以刷新）

## 安全考虑

### 1. 密钥管理
- 生产环境必须使用强随机密钥
- 定期轮换JWT签名密钥
- 密钥应通过环境变量或安全配置管理系统提供

### 2. 令牌生命周期
- 默认令牌有效期为24小时
- 支持令牌刷新机制
- 过期令牌自动失效

### 3. 传输安全
- 生产环境必须使用HTTPS
- JWT令牌应通过Authorization头传输
- 避免在URL参数中传递令牌

### 4. 错误处理
- 统一的错误响应格式
- 不泄露敏感信息
- 详细的错误日志记录

## 与EdgeAPI的兼容性

### 1. 用户认证
- 完全复用EdgeAPI的用户认证逻辑
- 支持用户名、邮箱、手机号登录
- 遵循EdgeAPI的密码验证规则

### 2. 权限控制
- 继承EdgeAPI的用户权限体系
- 支持Enterprise Admin级别的权限范围
- 数据隔离与EdgeAPI保持一致

### 3. 数据模型
- 用户ID与EdgeAPI完全对应
- 用户信息结构保持兼容
- 无需额外的用户数据同步

## 使用示例

### cURL示例

```bash
# 1. 用户登录
curl -X POST "http://localhost:8080/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "enterprise_user",
    "password": "secure_password"
  }'

# 2. 使用JWT令牌访问API
curl -X GET "http://localhost:8080/api/v1/users" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 3. 刷新令牌
curl -X POST "http://localhost:8080/api/v1/auth/refresh" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### JavaScript示例

```javascript
// 登录获取JWT令牌
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    username: 'enterprise_user',
    password: 'secure_password'
  })
});

const loginData = await loginResponse.json();
const token = loginData.data.access_token;

// 使用JWT令牌调用API
const apiResponse = await fetch('/api/v1/users', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

## 故障排除

### 常见错误

1. **401 Unauthorized**: 令牌无效或过期
2. **400 Bad Request**: 请求格式错误
3. **500 Internal Server Error**: 服务器内部错误

### 调试建议

1. 检查JWT令牌格式和签名
2. 验证配置文件中的密钥设置
3. 查看服务器日志获取详细错误信息
4. 确认EdgeAPI服务连接正常
