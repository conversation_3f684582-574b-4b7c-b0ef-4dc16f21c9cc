package auth

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestJWTManager_GenerateToken(t *testing.T) {
	secretKey := "test_secret_key_for_jwt_testing"
	tokenDuration := time.Hour
	issuer := "EdgeOpenAPI_Test"

	jwtManager := NewJWTManager(secretKey, tokenDuration, issuer)

	tests := []struct {
		name     string
		userID   int64
		username string
		email    string
		mobile   string
		role     string
		wantErr  bool
	}{
		{
			name:     "valid user with email",
			userID:   12345,
			username: "testuser",
			email:    "<EMAIL>",
			mobile:   "",
			role:     "user",
			wantErr:  false,
		},
		{
			name:     "valid user with mobile",
			userID:   67890,
			username: "mobileuser",
			email:    "",
			mobile:   "13800138000",
			role:     "user",
			wantErr:  false,
		},
		{
			name:     "valid user with username only",
			userID:   11111,
			username: "usernameonly",
			email:    "",
			mobile:   "",
			role:     "user",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := jwtManager.GenerateToken(tt.userID, tt.username, tt.email, tt.mobile, tt.role)
			
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, token)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, token)
				
				// Verify token can be validated
				claims, err := jwtManager.ValidateToken(token)
				require.NoError(t, err)
				assert.Equal(t, tt.userID, claims.UserID)
				assert.Equal(t, tt.username, claims.Username)
				assert.Equal(t, tt.email, claims.Email)
				assert.Equal(t, tt.mobile, claims.Mobile)
				assert.Equal(t, tt.role, claims.Role)
				assert.Equal(t, issuer, claims.Issuer)
			}
		})
	}
}

func TestJWTManager_ValidateToken(t *testing.T) {
	secretKey := "test_secret_key_for_jwt_testing"
	tokenDuration := time.Hour
	issuer := "EdgeOpenAPI_Test"

	jwtManager := NewJWTManager(secretKey, tokenDuration, issuer)

	// Generate a valid token
	userID := int64(12345)
	username := "testuser"
	email := "<EMAIL>"
	role := "user"

	validToken, err := jwtManager.GenerateToken(userID, username, email, "", role)
	require.NoError(t, err)

	tests := []struct {
		name      string
		token     string
		wantErr   bool
		wantClaims *JWTClaims
	}{
		{
			name:    "valid token",
			token:   validToken,
			wantErr: false,
		},
		{
			name:    "invalid token format",
			token:   "invalid.token.format",
			wantErr: true,
		},
		{
			name:    "empty token",
			token:   "",
			wantErr: true,
		},
		{
			name:    "malformed token",
			token:   "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.malformed.signature",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims, err := jwtManager.ValidateToken(tt.token)
			
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, claims)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, claims)
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, username, claims.Username)
				assert.Equal(t, email, claims.Email)
				assert.Equal(t, role, claims.Role)
			}
		})
	}
}

func TestJWTManager_RefreshToken(t *testing.T) {
	secretKey := "test_secret_key_for_jwt_testing"
	tokenDuration := time.Minute // Short duration for testing
	issuer := "EdgeOpenAPI_Test"

	jwtManager := NewJWTManager(secretKey, tokenDuration, issuer)

	userID := int64(12345)
	username := "testuser"
	email := "<EMAIL>"
	role := "user"

	// Generate a token that will be eligible for refresh soon
	originalToken, err := jwtManager.GenerateToken(userID, username, email, "", role)
	require.NoError(t, err)

	tests := []struct {
		name      string
		token     string
		waitTime  time.Duration
		wantErr   bool
		errMsg    string
	}{
		{
			name:     "token not eligible for refresh yet",
			token:    originalToken,
			waitTime: 0,
			wantErr:  true,
			errMsg:   "token is not eligible for refresh yet",
		},
		{
			name:     "invalid token",
			token:    "invalid.token.format",
			waitTime: 0,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.waitTime > 0 {
				time.Sleep(tt.waitTime)
			}
			
			newToken, err := jwtManager.RefreshToken(tt.token)
			
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, newToken)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, newToken)
				assert.NotEqual(t, tt.token, newToken)
				
				// Verify new token is valid
				claims, err := jwtManager.ValidateToken(newToken)
				require.NoError(t, err)
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, username, claims.Username)
			}
		})
	}
}

func TestJWTManager_ExtractUserID(t *testing.T) {
	secretKey := "test_secret_key_for_jwt_testing"
	tokenDuration := time.Hour
	issuer := "EdgeOpenAPI_Test"

	jwtManager := NewJWTManager(secretKey, tokenDuration, issuer)

	userID := int64(12345)
	username := "testuser"
	role := "user"

	validToken, err := jwtManager.GenerateToken(userID, username, "", "", role)
	require.NoError(t, err)

	tests := []struct {
		name       string
		token      string
		wantUserID int64
		wantErr    bool
	}{
		{
			name:       "valid token",
			token:      validToken,
			wantUserID: userID,
			wantErr:    false,
		},
		{
			name:       "invalid token",
			token:      "invalid.token",
			wantUserID: 0,
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			extractedUserID, err := jwtManager.ExtractUserID(tt.token)
			
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, int64(0), extractedUserID)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantUserID, extractedUserID)
			}
		})
	}
}

func TestJWTManager_IsTokenExpired(t *testing.T) {
	secretKey := "test_secret_key_for_jwt_testing"
	tokenDuration := time.Millisecond * 100 // Very short duration
	issuer := "EdgeOpenAPI_Test"

	jwtManager := NewJWTManager(secretKey, tokenDuration, issuer)

	userID := int64(12345)
	username := "testuser"
	role := "user"

	// Generate a token with very short duration
	token, err := jwtManager.GenerateToken(userID, username, "", "", role)
	require.NoError(t, err)

	// Token should not be expired immediately
	expired := jwtManager.IsTokenExpired(token)
	t.Logf("Token expired immediately: %v", expired)
	if expired {
		// Debug: validate the token to see what's wrong
		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			t.Logf("Token validation error: %v", err)
		} else {
			t.Logf("Token expires at: %v, current time: %v", claims.RegisteredClaims.ExpiresAt.Time, time.Now())
		}
	}
	assert.False(t, expired)

	// Wait for token to expire
	time.Sleep(time.Millisecond * 200)

	// Token should now be expired
	assert.True(t, jwtManager.IsTokenExpired(token))

	// Invalid token should be considered expired
	assert.True(t, jwtManager.IsTokenExpired("invalid.token"))
}

func TestJWTManager_DifferentSecretKeys(t *testing.T) {
	secretKey1 := "secret_key_1"
	secretKey2 := "secret_key_2"
	tokenDuration := time.Hour
	issuer := "EdgeOpenAPI_Test"

	jwtManager1 := NewJWTManager(secretKey1, tokenDuration, issuer)
	jwtManager2 := NewJWTManager(secretKey2, tokenDuration, issuer)

	userID := int64(12345)
	username := "testuser"
	role := "user"

	// Generate token with first manager
	token, err := jwtManager1.GenerateToken(userID, username, "", "", role)
	require.NoError(t, err)

	// First manager should validate successfully
	claims, err := jwtManager1.ValidateToken(token)
	assert.NoError(t, err)
	assert.NotNil(t, claims)

	// Second manager should fail validation (different secret key)
	claims, err = jwtManager2.ValidateToken(token)
	assert.Error(t, err)
	assert.Nil(t, claims)
}
