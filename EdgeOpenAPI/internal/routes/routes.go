package routes

import (
	"net/http"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/handlers"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.Engine, edgeAPIClient *grpc.EdgeAPIClient) {
	// Health check endpoints (no auth required)
	router.GET("/health", healthCheck)
	router.GET("/ping", ping)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes
		authHandler := handlers.NewAuthHandler(edgeAPIClient)
		auth := v1.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/validate", authHandler.ValidateToken)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/access-keys", authHandler.CreateAccessKey)
			auth.GET("/access-keys", authHandler.ListAccessKeys)
			auth.DELETE("/access-keys/:id", authHandler.DeleteAccessKey)
		}

		// User management routes
		userHandler := handlers.NewUserHandler(edgeAPIClient)
		users := v1.Group("/users")
		{
			users.GET("", userHandler.ListUsers)
			users.POST("", userHandler.CreateUser)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
		}

		// TODO: Add more route groups for other modules
		// - CDN services (/services)
		// - Node clusters (/clusters)
		// - Nodes (/nodes)
		// - Statistics (/stats)
		// - Billing (/billing)
	}
}

// healthCheck returns the health status of the service
func healthCheck(c *gin.Context) {
	converter.SuccessResponse(c, gin.H{
		"status":  "healthy",
		"service": "EdgeOpenAPI",
		"version": "1.0.0",
	})
}

// ping returns a simple pong response
func ping(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "pong",
	})
}
