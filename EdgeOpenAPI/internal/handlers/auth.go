package handlers

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/auth"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/config"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/grpc"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/middleware"
	"github.com/TeaOSLab/EdgeOpenAPI/pkg/converter"
	"github.com/gin-gonic/gin"
	"google.golang.org/grpc/metadata"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	edgeAPIClient *grpc.EdgeAPIClient
	jwtManager    *auth.JWTManager
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(edgeAPIClient *grpc.EdgeAPIClient) *AuthHandler {
	cfg := config.GetConfig()
	jwtManager := auth.NewJWTManager(
		cfg.JWT.SecretKey,
		cfg.JWT.TokenDuration,
		cfg.JWT.Issuer,
	)

	return &AuthHandler{
		edgeAPIClient: edgeAPIClient,
		jwtManager:    jwtManager,
	}
}

// Login handles POST /api/v1/auth/login
func (h *AuthHandler) Login(c *gin.Context) {
	var req converter.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Validate input
	if len(req.Username) == 0 || len(req.Password) == 0 {
		converter.ValidationErrorResponse(c, gin.H{
			"username": "Username is required",
			"password": "Password is required",
		})
		return
	}

	// Create API context for gRPC call (requires API node credentials)
	ctx := h.edgeAPIClient.CreateAPIContext()

	// Convert to protobuf request
	pbReq := converter.LoginUserToProto(&req)

	// Call EdgeAPI to validate user credentials
	resp, err := h.edgeAPIClient.UserService.LoginUser(ctx, pbReq)
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// Convert response
	loginResp := converter.LoginResponseFromProto(resp)

	if !loginResp.Success {
		// Login failed
		converter.ErrorResponse(c, http.StatusUnauthorized, "Login failed", gin.H{
			"type":    "authentication_error",
			"details": loginResp.Message,
		})
		return
	}

	// Login successful - generate JWT token
	jwtToken, err := h.jwtManager.GenerateToken(
		loginResp.UserID,
		req.Username,
		"", // email - will be populated from user info if needed
		"", // mobile - will be populated from user info if needed
		"user", // role
	)
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	// Return JWT token
	loginResp.AccessToken = jwtToken
	loginResp.TokenType = "Bearer"
	loginResp.Message = "Login successful"

	converter.SuccessResponse(c, loginResp)
}

// CreateAccessKey handles POST /api/v1/auth/access-keys
func (h *AuthHandler) CreateAccessKey(c *gin.Context) {
	var req converter.CreateAccessKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		converter.ValidationErrorResponse(c, err.Error())
		return
	}

	// Get user ID from authenticated context
	// Note: This requires the user to be authenticated with existing API keys
	accessToken := middleware.GetAccessTokenFromContext(c)
	if accessToken == "" {
		converter.UnauthorizedResponse(c, "Authentication required to create access keys")
		return
	}

	// For now, we'll need to get the user ID from the access token
	// This is a simplified implementation - in a real scenario, you'd decode the token
	// or make a call to get the current user info

	// TODO: Implement proper user ID extraction from access token
	// For now, we'll return an error suggesting the proper flow
	converter.ErrorResponse(c, http.StatusNotImplemented, "Access key creation not yet implemented", gin.H{
		"type":    "not_implemented_error",
		"details": "Please use the EdgeAPI admin interface to create access keys for now",
	})
}

// ListAccessKeys handles GET /api/v1/auth/access-keys
func (h *AuthHandler) ListAccessKeys(c *gin.Context) {
	// Get authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	_ = accessToken // Mark as used to avoid compiler warning

	// TODO: Get user ID from access token
	// For now, we'll return an error suggesting the proper flow
	converter.ErrorResponse(c, http.StatusNotImplemented, "Access key listing not yet implemented", gin.H{
		"type":    "not_implemented_error",
		"details": "Please use the EdgeAPI admin interface to manage access keys for now",
	})
}

// DeleteAccessKey handles DELETE /api/v1/auth/access-keys/{id}
func (h *AuthHandler) DeleteAccessKey(c *gin.Context) {
	// Parse access key ID from URL
	accessKeyIdStr := c.Param("id")
	accessKeyId, err := strconv.ParseInt(accessKeyIdStr, 10, 64)
	if err != nil {
		converter.ValidationErrorResponse(c, gin.H{
			"field":   "id",
			"message": "Invalid access key ID",
		})
		return
	}

	// Get authenticated context
	accessToken := middleware.GetAccessTokenFromContext(c)
	ctx := metadata.NewOutgoingContext(context.Background(), metadata.Pairs(
		"authorization", "Bearer "+accessToken,
	))

	// Call EdgeAPI to delete access key
	_, err = h.edgeAPIClient.UserAccessKeyService.DeleteUserAccessKey(ctx, &pb.DeleteUserAccessKeyRequest{
		UserAccessKeyId: accessKeyId,
	})
	if err != nil {
		converter.InternalErrorResponse(c, err)
		return
	}

	converter.SuccessResponse(c, gin.H{
		"message": "Access key deleted successfully",
	})
}

// ValidateToken handles POST /api/v1/auth/validate
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	// This endpoint validates the current JWT token
	claims := middleware.GetJWTClaimsFromContext(c)
	if claims == nil {
		converter.UnauthorizedResponse(c, "No valid token found")
		return
	}

	// If we reach here, the token is valid (middleware already validated it)
	converter.SuccessResponse(c, gin.H{
		"valid":    true,
		"message":  "Token is valid",
		"user_id":  claims.UserID,
		"username": claims.Username,
		"expires_at": claims.RegisteredClaims.ExpiresAt.Unix(),
	})
}

// RefreshToken handles POST /api/v1/auth/refresh
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// 从Authorization头中提取当前令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		converter.UnauthorizedResponse(c, "Missing authorization header")
		return
	}

	tokenParts := strings.SplitN(authHeader, " ", 2)
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		converter.ErrorResponse(c, http.StatusBadRequest, "Invalid authorization header format", gin.H{
			"type":    "validation_error",
			"details": "Authorization header must be in format: Bearer <token>",
		})
		return
	}

	currentToken := tokenParts[1]

	// 刷新令牌
	newToken, err := h.jwtManager.RefreshToken(currentToken)
	if err != nil {
		converter.ErrorResponse(c, http.StatusBadRequest, "Token refresh failed", gin.H{
			"type":    "token_refresh_error",
			"details": err.Error(),
		})
		return
	}

	converter.SuccessResponse(c, gin.H{
		"access_token": newToken,
		"token_type":   "Bearer",
		"message":      "Token refreshed successfully",
	})
}
