package middleware

import (
	"net/http"
	"strings"

	"github.com/TeaOSLab/EdgeOpenAPI/internal/auth"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/config"
	"github.com/gin-gonic/gin"
)

// JWTMiddleware JWT认证中间件
func JWTMiddleware() gin.HandlerFunc {
	cfg := config.GetConfig()
	jwtManager := auth.NewJWTManager(
		cfg.JWT.SecretKey,
		cfg.JWT.TokenDuration,
		cfg.JWT.Issuer,
	)

	return func(c *gin.Context) {
		// 跳过不需要认证的端点
		if c.Request.URL.Path == "/health" ||
			c.Request.URL.Path == "/ping" ||
			c.Request.URL.Path == "/api/v1/auth/login" {
			c.Next()
			return
		}

		// 从Authorization头中提取JWT令牌
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Missing authorization header",
				"error": gin.H{
					"type":    "authentication_error",
					"details": "Authorization header is required",
				},
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenParts := strings.SplitN(authHeader, " ", 2)
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Invalid authorization header format",
				"error": gin.H{
					"type":    "authentication_error",
					"details": "Authorization header must be in format: Bearer <token>",
				},
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// 验证JWT令牌
		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    http.StatusUnauthorized,
				"message": "Invalid or expired token",
				"error": gin.H{
					"type":    "authentication_error",
					"details": err.Error(),
				},
			})
			c.Abort()
			return
		}

		// 将用户信息设置到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_email", claims.Email)
		c.Set("user_mobile", claims.Mobile)
		c.Set("user_role", claims.Role)
		c.Set("jwt_claims", claims)

		c.Next()
	}
}

// GetUserIDFromContext 从上下文中获取用户ID
func GetUserIDFromContext(c *gin.Context) int64 {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(int64); ok {
			return id
		}
	}
	return 0
}

// GetUsernameFromContext 从上下文中获取用户名
func GetUsernameFromContext(c *gin.Context) string {
	if username, exists := c.Get("username"); exists {
		if name, ok := username.(string); ok {
			return name
		}
	}
	return ""
}

// GetJWTClaimsFromContext 从上下文中获取JWT声明
func GetJWTClaimsFromContext(c *gin.Context) *auth.JWTClaims {
	if claims, exists := c.Get("jwt_claims"); exists {
		if jwtClaims, ok := claims.(*auth.JWTClaims); ok {
			return jwtClaims
		}
	}
	return nil
}

// CreateUserContext 为gRPC调用创建带用户信息的上下文
func CreateUserContext(c *gin.Context) gin.H {
	userID := GetUserIDFromContext(c)
	username := GetUsernameFromContext(c)
	
	return gin.H{
		"user_id":  userID,
		"username": username,
	}
}
