package grpc

import (
	"context"
	"crypto/tls"
	"encoding/base64"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/TeaOSLab/EdgeCommon/pkg/rpc/pb"
	"github.com/TeaOSLab/EdgeOpenAPI/internal/config"
	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/encoding/gzip"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"
)

// EdgeAPIClient wraps all EdgeAPI gRPC service clients with connection pooling
type EdgeAPIClient struct {
	conns                 []*grpc.ClientConn
	currentConnIndex      int
	mutex                 sync.RWMutex
	endpoints             []string
	APIAccessTokenService pb.APIAccessTokenServiceClient
	UserService           pb.UserServiceClient
	ServerService         pb.ServerServiceClient
	NodeService           pb.NodeServiceClient
	NodeClusterService    pb.NodeClusterServiceClient
	UserBillService       pb.UserBillServiceClient
	UserAccessKeyService  pb.UserAccessKeyServiceClient
	// Note: StatService doesn't exist in EdgeCommon, will use specific stat services as needed
}

// NewEdgeAPIClient creates a new EdgeAPI client with connection pooling
func NewEdgeAPIClient(endpoint string) (*EdgeAPIClient, error) {
	endpoints := []string{endpoint} // For now, single endpoint, can be extended to multiple

	client := &EdgeAPIClient{
		endpoints: endpoints,
	}

	err := client.init()
	if err != nil {
		return nil, err
	}

	return client, nil
}

// init initializes the gRPC connections
func (c *EdgeAPIClient) init() error {
	var conns []*grpc.ClientConn

	for _, endpoint := range c.endpoints {
		conn, err := c.createConnection(endpoint)
		if err != nil {
			// Close any successful connections before returning error
			for _, existingConn := range conns {
				existingConn.Close()
			}
			return err
		}
		conns = append(conns, conn)
	}

	if len(conns) == 0 {
		return errors.New("no available endpoints")
	}

	c.conns = conns

	// Initialize service clients using the first connection
	conn := c.pickConn()
	c.APIAccessTokenService = pb.NewAPIAccessTokenServiceClient(conn)
	c.UserService = pb.NewUserServiceClient(conn)
	c.ServerService = pb.NewServerServiceClient(conn)
	c.NodeService = pb.NewNodeServiceClient(conn)
	c.NodeClusterService = pb.NewNodeClusterServiceClient(conn)
	c.UserBillService = pb.NewUserBillServiceClient(conn)
	c.UserAccessKeyService = pb.NewUserAccessKeyServiceClient(conn)

	return nil
}

// createConnection creates a single gRPC connection with optimized settings
func (c *EdgeAPIClient) createConnection(endpoint string) (*grpc.ClientConn, error) {
	// Parse endpoint to determine if it's HTTP or HTTPS
	u, err := url.Parse(endpoint)
	if err != nil {
		// If parsing fails, assume it's a plain address
		u = &url.URL{Scheme: "http", Host: endpoint}
	}

	var callOptions = grpc.WithDefaultCallOptions(
		grpc.MaxCallRecvMsgSize(128<<20), // 128MB
		grpc.MaxCallSendMsgSize(128<<20), // 128MB
		grpc.UseCompressor(gzip.Name),
	)

	var keepaliveParams = grpc.WithKeepaliveParams(keepalive.ClientParameters{
		Time:                30 * time.Second, // Send keepalive pings every 30 seconds
		Timeout:             5 * time.Second,  // Wait 5 seconds for ping ack
		PermitWithoutStream: true,             // Send pings even without active streams
	})

	var conn *grpc.ClientConn
	if u.Scheme == "https" {
		conn, err = grpc.Dial(u.Host,
			grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{
				InsecureSkipVerify: true, // For development, should be false in production
			})),
			callOptions,
			keepaliveParams,
		)
	} else {
		conn, err = grpc.Dial(u.Host,
			grpc.WithTransportCredentials(insecure.NewCredentials()),
			callOptions,
			keepaliveParams,
		)
	}

	return conn, err
}

// pickConn picks a connection using round-robin
func (c *EdgeAPIClient) pickConn() *grpc.ClientConn {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if len(c.conns) == 0 {
		return nil
	}

	// Simple round-robin selection
	conn := c.conns[c.currentConnIndex]
	c.currentConnIndex = (c.currentConnIndex + 1) % len(c.conns)

	// Check if connection is healthy, if not try to reconnect
	if conn.GetState() == connectivity.TransientFailure || conn.GetState() == connectivity.Shutdown {
		// Try to reconnect
		if c.currentConnIndex > 0 {
			newConn, err := c.createConnection(c.endpoints[c.currentConnIndex-1])
			if err == nil {
				conn.Close()
				c.conns[c.currentConnIndex-1] = newConn
				conn = newConn
			}
		}
	}

	return conn
}

// Close closes all gRPC connections
func (c *EdgeAPIClient) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	var lastErr error
	for _, conn := range c.conns {
		if err := conn.Close(); err != nil {
			lastErr = err
		}
	}
	c.conns = nil
	return lastErr
}

// GetAPIAccessToken is a convenience method to get access token
func (c *EdgeAPIClient) GetAPIAccessToken(ctx context.Context, accessKeyId, accessKey string) (*pb.GetAPIAccessTokenResponse, error) {
	return c.APIAccessTokenService.GetAPIAccessToken(ctx, &pb.GetAPIAccessTokenRequest{
		Type:        "user",
		AccessKeyId: accessKeyId,
		AccessKey:   accessKey,
	})
}

// CreateAPIContext creates a context with API node credentials for gRPC calls
func (c *EdgeAPIClient) CreateAPIContext() context.Context {
	cfg := config.GetConfig()
	if cfg == nil {
		// Fallback to default values if config is not available
		nodeId := "EdgeOpenAPI_Node_001"
		timestamp := time.Now().Unix()
		tokenBytes := []byte(`{"timestamp":` + strconv.FormatInt(timestamp, 10) + `,"type":"api","userId":0}`)
		token := base64.StdEncoding.EncodeToString(tokenBytes)
		return metadata.AppendToOutgoingContext(context.Background(), "nodeId", nodeId, "token", token)
	}

	// Use configuration values
	nodeId := cfg.APINode.NodeID

	// Create a proper token with timestamp and type
	// TODO: Implement proper encryption using EdgeAPI's encryption method like EdgeAdmin does
	timestamp := time.Now().Unix()
	tokenBytes := []byte(fmt.Sprintf(`{"timestamp":%d,"type":"api","userId":0}`, timestamp))
	token := base64.StdEncoding.EncodeToString(tokenBytes)

	// Create context with metadata
	ctx := context.Background()
	ctx = metadata.AppendToOutgoingContext(ctx, "nodeid", nodeId, "token", token)
	return ctx
}
