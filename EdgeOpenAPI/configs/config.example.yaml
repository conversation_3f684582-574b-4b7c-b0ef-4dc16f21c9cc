# EdgeOpenAPI Configuration Example
# Copy this file to config.yaml and modify as needed

# Server configuration
server:
  host: "0.0.0.0"
  port: 8080
  mode: "release" # gin mode: debug, release, test

# EdgeAPI connection settings
edgeapi:
  endpoint: "localhost:8001"
  timeout: "30s"
  keepalive:
    time: "10s"
    timeout: "1s"
    permit_without_stream: true

# API Node credentials for EdgeAPI authentication
api_node:
  node_id: "your_api_node_id_here"  # API节点ID，需要在EdgeAPI中注册
  secret: "your_api_node_secret_here"  # API节点密钥

# Logging configuration
logging:
  level: "info" # debug, info, warn, error
  format: "json" # json, text
  output: "stdout" # stdout, stderr, or file path

# CORS settings
cors:
  allowed_origins: ["*"]
  allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allowed_headers: ["Origin", "Content-Type", "Accept", "Authorization", "X-API-Key-ID", "X-API-Key"]
  expose_headers: ["Content-Length"]
  allow_credentials: true

# Rate limiting (optional)
rate_limit:
  enabled: false
  requests_per_minute: 100
  burst: 10

# Security settings
security:
  request_timeout: "30s"
  max_request_size: "10MB"

# JWT settings
jwt:
  secret_key: "your_jwt_secret_key_here_change_in_production"  # JWT签名密钥，生产环境必须更改
  token_duration: "24h"  # JWT令牌有效期
  issuer: "EdgeOpenAPI"  # JWT发行者
  refresh_threshold: "30m"  # 令牌刷新阈值（过期前多久可以刷新）
